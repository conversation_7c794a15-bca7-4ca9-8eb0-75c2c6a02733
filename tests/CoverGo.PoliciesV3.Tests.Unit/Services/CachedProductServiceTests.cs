using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Caching;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using CoverGo.Products.Client;
using DomainProductId = CoverGo.PoliciesV3.Domain.Policies.ProductId;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for CachedProductService to verify caching behavior and method delegation
/// </summary>
[Trait("Category", "Unit")]
[Trait("Component", "Infrastructure")]
[Trait("Feature", "Product")]
public class CachedProductServiceTests : IDisposable
{
    private readonly Mock<ITenantProvider> _mockTenantProvider;
    private readonly Mock<IProductService> _mockInnerService;
    private readonly Mock<CacheProvider> _mockCacheProvider;
    private readonly Fixture _fixture;
    private readonly CancellationToken _cancellationToken;

    public CachedProductServiceTests()
    {
        _mockTenantProvider = new Mock<ITenantProvider>();
        _mockInnerService = new Mock<IProductService>();
        _mockCacheProvider = new Mock<CacheProvider>();
        _fixture = new Fixture();
        _cancellationToken = CancellationToken.None;
    }

    #region Test Helpers

    private CachedProductService CreateService() => new(
        _mockTenantProvider.Object,
        _mockInnerService.Object,
        _mockCacheProvider.Object);

    private ProductId CreateProductId() => new()
    {
        Plan = _fixture.Create<string>(),
        Type = _fixture.Create<string>(),
        Version = _fixture.Create<string>()
    };

    private DomainProductId CreateDomainProductId() => new(
        _fixture.Create<string>(),
        _fixture.Create<string>(),
        _fixture.Create<string>());

    private TenantId CreateTenantId() => new(_fixture.Create<string>());

    private string CreateValidSchema() => """
        {
            "type": "object",
            "properties": {
                "name": { "type": "string", "required": true },
                "age": { "type": "number", "minimum": 0 },
                "email": { "type": "string", "format": "email" }
            }
        }
        """;

    private void SetupTenantProvider(TenantId? tenantId = null)
    {
        tenantId ??= CreateTenantId();
        _mockTenantProvider
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = tenantId;
                return true;
            });
    }

    private void SetupTenantProviderFailure()
    {
        _mockTenantProvider
            .Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? tenant) =>
            {
                tenant = null;
                return false;
            });
    }

    private string GetExpectedCacheKey(TenantId tenantId, ProductId productId)
    {
        return $"schema:product-member:{tenantId.Value}:{productId}";
    }

    #endregion

    #region Service Instantiation Tests

    [Fact]
    public void CachedProductService_CanBeInstantiated()
    {
        // Arrange & Act
        CachedProductService service = CreateService();

        // Assert
        service.Should().NotBeNull();
        service.Should().BeAssignableTo<IProductService>();
    }

    [Fact]
    public void CachedProductService_ShouldImplementIProductService()
    {
        // Arrange & Act
        CachedProductService service = CreateService();

        // Assert - Service should implement the interface correctly
        service.Should().BeAssignableTo<IProductService>();

        // Verify service has the expected methods
        Type serviceType = service.GetType();
        serviceType.GetMethod(nameof(IProductService.GetProductMemberSchema)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetProductPackageType)).Should().NotBeNull();
        serviceType.GetMethod(nameof(IProductService.GetAvailablePlanIds)).Should().NotBeNull();
    }

    #endregion

    #region GetProductMemberSchema - Cache Hit Tests

    [Fact]
    public async Task GetProductMemberSchema_WithCacheHit_ShouldReturnCachedValueWithoutCallingInnerService()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId = CreateProductId();
        string cachedSchema = CreateValidSchema();
        string expectedCacheKey = GetExpectedCacheKey(tenantId, productId);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey, _cancellationToken))
            .ReturnsAsync(cachedSchema);

        CachedProductService service = CreateService();

        // Act
        string? result = await service.GetProductMemberSchema(productId, _cancellationToken);

        // Assert
        result.Should().Be(cachedSchema);

        // Verify cache was checked
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey, _cancellationToken),
            Times.Once);

        // Verify inner service was NOT called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()),
            Times.Never);

        // Verify nothing was set in cache (since we had a hit)
        _mockCacheProvider.Verify(
            x => x.SetAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetProductMemberSchema_WithCacheHitAndDifferentProductIds_ShouldUseDifferentCacheKeys()
    {
        // Arrange
        TenantId tenantId = CreateTenantId();
        ProductId productId1 = CreateProductId();
        ProductId productId2 = CreateProductId();
        string cachedSchema1 = CreateValidSchema();
        string cachedSchema2 = """{"type": "object", "properties": {"company": {"type": "string"}}}""";

        string expectedCacheKey1 = GetExpectedCacheKey(tenantId, productId1);
        string expectedCacheKey2 = GetExpectedCacheKey(tenantId, productId2);

        SetupTenantProvider(tenantId);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey1, _cancellationToken))
            .ReturnsAsync(cachedSchema1);
        _mockCacheProvider
            .Setup(x => x.GetAsync<string>(expectedCacheKey2, _cancellationToken))
            .ReturnsAsync(cachedSchema2);

        CachedProductService service = CreateService();

        // Act
        string? result1 = await service.GetProductMemberSchema(productId1, _cancellationToken);
        string? result2 = await service.GetProductMemberSchema(productId2, _cancellationToken);

        // Assert
        result1.Should().Be(cachedSchema1);
        result2.Should().Be(cachedSchema2);

        // Verify different cache keys were used
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey1, _cancellationToken),
            Times.Once);
        _mockCacheProvider.Verify(
            x => x.GetAsync<string>(expectedCacheKey2, _cancellationToken),
            Times.Once);

        // Verify inner service was never called
        _mockInnerService.Verify(
            x => x.GetProductMemberSchema(It.IsAny<ProductId>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    #endregion

    protected virtual void Dispose(bool disposing)
    {
        if (disposing)
        {
            // Clean up any resources if needed
        }
    }

    void IDisposable.Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
