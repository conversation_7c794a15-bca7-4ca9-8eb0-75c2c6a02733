using System.Reflection;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace CoverGo.PoliciesV3.Tests.Unit.Infrastructure.Repositories;

/// <summary>
/// Unit tests for PolicyMemberUploadRepository business-specific methods.
/// Tests the specialized repository methods that provide clear business intent.
/// These tests focus on the business logic and interface contracts.
///
/// IMPORTANT: StartValidationIfNotLockedAsync Implementation Details
/// ================================================================
/// The StartValidationIfNotLockedAsync method performs two key operations:
/// 1. Clears existing validation errors using ExecuteDeleteAsync on ValidationErrors
/// 2. Updates upload status to VALIDATING using ExecuteUpdateAsync
///
/// This addresses the DbUpdateConcurrencyException issue that occurred when:
/// - Previous validation runs left validation errors in the database
/// - New validation attempts tried to insert errors with the same (UploadId, RowIndex) keys
/// - The in-memory ValidationErrors collection was cleared but database wasn't
///
/// Testing Approach:
/// - Mock-based tests verify interface contracts and business logic
/// - Integration tests with real database would be needed to fully test ExecuteDeleteAsync
/// - In-memory database provider doesn't support ExecuteDeleteAsync operations
/// </summary>
public class PolicyMemberUploadRepositoryTests
{
    private readonly Mock<IPolicyMemberUploadRepository> _mockRepository;
    private readonly Fixture _fixture;

    public PolicyMemberUploadRepositoryTests()
    {
        _fixture = new Fixture();
        _fixture.Customize<PolicyMemberId>(c => c.FromFactory(() => PolicyMemberId.New));
        _fixture.Customize<PolicyMemberUploadId>(c => c.FromFactory(() => PolicyMemberUploadId.New));
        _fixture.Customize<PolicyId>(c => c.FromFactory(() => PolicyId.New));
        _fixture.Customize<DateOnly>(c => c.FromFactory(() => DateOnly.FromDateTime(DateTime.Today.AddDays(_fixture.Create<int>() % 365))));
        _mockRepository = new Mock<IPolicyMemberUploadRepository>();
    }

    #region StartValidationIfNotLockedAsync Tests

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithValidId_ShouldReturnTrue()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithLockedStatus_ShouldReturnFalse()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithNonExistentId_ShouldReturnFalse()
    {
        // Arrange
        PolicyMemberUploadId nonExistentId = PolicyMemberUploadId.New;
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(nonExistentId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(nonExistentId, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void StartValidationIfNotLockedAsync_InterfaceContract_ShouldBeCorrectlyDefined()
    {
        // This test verifies the interface contract exists and has the expected signature
        MethodInfo? method = typeof(IPolicyMemberUploadRepository).GetMethod(nameof(IPolicyMemberUploadRepository.StartValidationIfNotLockedAsync));

        method.Should().NotBeNull();
        method!.ReturnType.Should().Be(typeof(Task<bool>));

        ParameterInfo[] parameters = method.GetParameters();
        parameters.Should().HaveCount(2);
        parameters[0].ParameterType.Should().Be(typeof(PolicyMemberUploadId));
        parameters[1].ParameterType.Should().Be(typeof(CancellationToken));
    }

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithExistingValidationErrors_ShouldClearErrorsAndReturnTrue()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;

        // Mock the repository to simulate the expected behavior:
        // 1. Clear existing validation errors using ExecuteDeleteAsync
        // 2. Update upload status to VALIDATING using ExecuteUpdateAsync
        // 3. Return true indicating successful operation
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithNoExistingValidationErrors_ShouldUpdateStatusAndReturnTrue()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;

        // Mock the repository to simulate the expected behavior:
        // 1. Attempt to clear validation errors (none exist, so no effect)
        // 2. Update upload status to VALIDATING using ExecuteUpdateAsync
        // 3. Return true indicating successful operation
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithConcurrencyException_ShouldReturnFalse()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;

        // Mock the repository to simulate a concurrency exception scenario
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task StartValidationIfNotLockedAsync_WithCancellationToken_ShouldPassTokenCorrectly()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        var cancellationToken = new CancellationToken();

        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, cancellationToken))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, cancellationToken);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, cancellationToken), Times.Once);
    }

    [Theory]
    [InlineData("CANCELING")]
    [InlineData("CANCELED")]
    public async Task StartValidationIfNotLockedAsync_WithLockedStatuses_ShouldReturnFalse(string lockedStatusString)
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;

        // Mock the repository to simulate locked status scenario
        _mockRepository
            .Setup(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        bool result = await _mockRepository.Object.StartValidationIfNotLockedAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().BeFalse();
        _mockRepository.Verify(x => x.StartValidationIfNotLockedAsync(uploadId, It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region CompleteValidationIfNotLockedAsync Tests

    [Fact]
    public async Task CompleteValidationIfNotLockedAsync_WithValidParameters_ShouldReturnTrue()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        _mockRepository
            .Setup(x => x.CompleteValidationIfNotLockedAsync(
                uploadId,
                PolicyMemberUploadStatus.VALIDATED,
                10,
                2,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.CompleteValidationIfNotLockedAsync(
            uploadId,
            PolicyMemberUploadStatus.VALIDATED,
            10,
            2,
            CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.CompleteValidationIfNotLockedAsync(
            uploadId,
            PolicyMemberUploadStatus.VALIDATED,
            10,
            2,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task CompleteValidationIfNotLockedAsync_WithLockedStatus_ShouldReturnFalse()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        _mockRepository
            .Setup(x => x.CompleteValidationIfNotLockedAsync(
                uploadId,
                PolicyMemberUploadStatus.VALIDATED,
                10,
                2,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        bool result = await _mockRepository.Object.CompleteValidationIfNotLockedAsync(
            uploadId,
            PolicyMemberUploadStatus.VALIDATED,
            10,
            2,
            CancellationToken.None);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CompleteValidationIfNotLockedAsync_InterfaceContract_ShouldBeCorrectlyDefined()
    {
        // This test verifies the interface contract exists and has the expected signature
        MethodInfo? method = typeof(IPolicyMemberUploadRepository).GetMethod(nameof(IPolicyMemberUploadRepository.CompleteValidationIfNotLockedAsync));

        method.Should().NotBeNull();
        method!.ReturnType.Should().Be(typeof(Task<bool>));

        ParameterInfo[] parameters = method.GetParameters();
        parameters.Should().HaveCount(5);
        parameters[0].ParameterType.Should().Be(typeof(PolicyMemberUploadId));
        parameters[1].ParameterType.Should().Be(typeof(PolicyMemberUploadStatus));
        parameters[2].ParameterType.Should().Be(typeof(int));
        parameters[3].ParameterType.Should().Be(typeof(int));
        parameters[4].ParameterType.Should().Be(typeof(CancellationToken));
    }

    #endregion

    #region FailValidationIfNotLockedAsync Tests

    [Fact]
    public async Task FailValidationIfNotLockedAsync_WithValidParameters_ShouldReturnTrue()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        const string errorMessage = "Validation failed due to system error";

        _mockRepository
            .Setup(x => x.FailValidationIfNotLockedAsync(uploadId, errorMessage, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.FailValidationIfNotLockedAsync(
            uploadId,
            errorMessage,
            CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.FailValidationIfNotLockedAsync(uploadId, errorMessage, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task FailValidationIfNotLockedAsync_WithLockedStatus_ShouldReturnFalse()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        const string errorMessage = "Validation failed due to system error";

        _mockRepository
            .Setup(x => x.FailValidationIfNotLockedAsync(uploadId, errorMessage, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        // Act
        bool result = await _mockRepository.Object.FailValidationIfNotLockedAsync(
            uploadId,
            errorMessage,
            CancellationToken.None);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void FailValidationIfNotLockedAsync_InterfaceContract_ShouldBeCorrectlyDefined()
    {
        // This test verifies the interface contract exists and has the expected signature
        MethodInfo? method = typeof(IPolicyMemberUploadRepository).GetMethod(nameof(IPolicyMemberUploadRepository.FailValidationIfNotLockedAsync));

        method.Should().NotBeNull();
        method!.ReturnType.Should().Be(typeof(Task<bool>));

        ParameterInfo[] parameters = method.GetParameters();
        parameters.Should().HaveCount(3);
        parameters[0].ParameterType.Should().Be(typeof(PolicyMemberUploadId));
        parameters[1].ParameterType.Should().Be(typeof(string));
        parameters[2].ParameterType.Should().Be(typeof(CancellationToken));
    }

    #endregion

    #region UpdateValidationProgressIfNotLockedAsync Tests

    [Fact]
    public async Task UpdateValidationProgressIfNotLockedAsync_WithValidParameters_ShouldReturnTrue()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        _mockRepository
            .Setup(x => x.UpdateValidationProgressIfNotLockedAsync(
                uploadId,
                PolicyMemberUploadStatus.VALIDATING,
                5,
                1,
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        // Act
        bool result = await _mockRepository.Object.UpdateValidationProgressIfNotLockedAsync(
            uploadId,
            PolicyMemberUploadStatus.VALIDATING,
            5,
            1,
            CancellationToken.None);

        // Assert
        result.Should().BeTrue();
        _mockRepository.Verify(x => x.UpdateValidationProgressIfNotLockedAsync(
            uploadId,
            PolicyMemberUploadStatus.VALIDATING,
            5,
            1,
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public void UpdateValidationProgressIfNotLockedAsync_InterfaceContract_ShouldBeCorrectlyDefined()
    {
        // This test verifies the interface contract exists and has the expected signature
        MethodInfo? method = typeof(IPolicyMemberUploadRepository).GetMethod(nameof(IPolicyMemberUploadRepository.UpdateValidationProgressIfNotLockedAsync));

        method.Should().NotBeNull();
        method!.ReturnType.Should().Be(typeof(Task<bool>));

        ParameterInfo[] parameters = method.GetParameters();
        parameters.Should().HaveCount(5);
        parameters[0].ParameterType.Should().Be(typeof(PolicyMemberUploadId));
        parameters[1].ParameterType.Should().Be(typeof(PolicyMemberUploadStatus));
        parameters[2].ParameterType.Should().Be(typeof(int));
        parameters[3].ParameterType.Should().Be(typeof(int));
        parameters[4].ParameterType.Should().Be(typeof(CancellationToken));
    }

    #endregion

    #region Repository Method Tests for CancelUpload Handler

    [Fact]
    public async Task FindByIdAsync_ForCancelUploadHandler_WithValidUploadId_ShouldReturnUpload()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;
        PolicyMemberUpload expectedUpload = CreateValidUpload(uploadId, PolicyMemberUploadStatus.CANCELING);

        _mockRepository.Setup(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedUpload);

        // Act
        PolicyMemberUpload result = await _mockRepository.Object.FindByIdAsync(uploadId, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(uploadId);
        result.Status.Should().Be(PolicyMemberUploadStatus.CANCELING);
        result.ImportedResults.Should().NotBeNull();
    }

    [Fact]
    public async Task FindByIdAsync_ForCancelUploadHandler_WithNonExistentUploadId_ShouldThrowNotFoundException()
    {
        // Arrange
        PolicyMemberUploadId uploadId = PolicyMemberUploadId.New;

        _mockRepository.Setup(x => x.FindByIdAsync(uploadId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new PolicyMemberUploadNotFoundException(uploadId.Value.ToString()));

        // Act & Assert
        await Assert.ThrowsAsync<PolicyMemberUploadNotFoundException>(
            () => _mockRepository.Object.FindByIdAsync(uploadId, CancellationToken.None));
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithStatusChange_ShouldUpdateSuccessfully()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadId.New, PolicyMemberUploadStatus.CANCELING);
        upload.CompleteCancellation(); // Change status to CANCELED

        _mockRepository.Setup(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUpload u, CancellationToken _) => u);

        // Act
        await _mockRepository.Object.UpdateAsync(upload, CancellationToken.None);

        // Assert
        upload.Status.Should().Be(PolicyMemberUploadStatus.CANCELED);
        _mockRepository.Verify(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithImportedResultsCleared_ShouldUpdateSuccessfully()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadId.New, PolicyMemberUploadStatus.CANCELING);

        // Add some imported results first
        var importedResult = PolicyMemberUploadImportedResult.CreateSuccess(
            upload.Id, 1, PolicyMemberId.New);
        upload.ImportedResults.Add(importedResult);

        // Clear them (simulating the cancellation cleanup)
        upload.ImportedResults.Clear();

        _mockRepository.Setup(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PolicyMemberUpload u, CancellationToken _) => u);

        // Act
        await _mockRepository.Object.UpdateAsync(upload, CancellationToken.None);

        // Assert
        upload.ImportedResults.Should().BeEmpty();
        _mockRepository.Verify(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task UpdateAsync_ForCancelUploadHandler_WithConcurrencyConflict_ShouldThrowException()
    {
        // Arrange
        PolicyMemberUpload upload = CreateValidUpload(PolicyMemberUploadId.New, PolicyMemberUploadStatus.CANCELING);

        _mockRepository.Setup(x => x.UpdateAsync(upload, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new DbUpdateConcurrencyException("Concurrency conflict"));

        // Act & Assert
        await Assert.ThrowsAsync<DbUpdateConcurrencyException>(
            () => _mockRepository.Object.UpdateAsync(upload, CancellationToken.None));
    }

    #endregion

    #region Helper Methods for CancelUpload Tests

    private PolicyMemberUpload CreateValidUpload(PolicyMemberUploadId id, PolicyMemberUploadStatus status)
    {
        var upload = PolicyMemberUpload.Create(
            PolicyId.New,
            "uploads/test-file.csv",
            100);

        // Use reflection to set the ID and status for testing
        PropertyInfo? idProperty = typeof(PolicyMemberUpload).BaseType?.GetProperty("Id");
        if (idProperty != null && idProperty.CanWrite)
        {
            idProperty.SetValue(upload, id);
        }
        else
        {
            FieldInfo? idField = typeof(PolicyMemberUpload).BaseType?.GetField("_id",
                BindingFlags.NonPublic | BindingFlags.Instance);
            idField?.SetValue(upload, id);
        }

        PropertyInfo? statusProperty = typeof(PolicyMemberUpload).GetProperty("Status");
        statusProperty?.SetValue(upload, status);

        // Clear and setup ImportedResults collection
        upload.ImportedResults.Clear();

        return upload;
    }

    #endregion
}
